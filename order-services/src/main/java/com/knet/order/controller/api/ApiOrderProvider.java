package com.knet.order.controller.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.dto.req.ReplaceProductRequest;
import com.knet.order.model.dto.resp.ReplaceProductResp;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;
import com.knet.order.service.IApiOrdersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 16:51
 * @description: 订单对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "订单服务-对外提供接口", description = "订单服务-对外提供接口")
public class ApiOrderProvider {
    @Resource
    private IApiOrdersService apiOrdersService;

    /**
     * 根据订单ID获取 订单商品明细信息
     *
     * @param prentOrderId 订单ID
     * @return 订单商品明细信息
     */
    @GetMapping("/order/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单商品明细信息", description = "供其他服务调用，获取订单商品明细信息")
    public HttpResult<List<SubOrderItemVo>> getOrderItemsByOrderId(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("获取订单商品明细信息: prentOrderId={}", prentOrderId);
        try {
            List<SubOrderItemVo> items = apiOrdersService.getOrderItemsByPrentOrderId(prentOrderId);
            if (CollUtil.isEmpty(items)) {
                log.warn("订单商品明细不存在: prentOrderId={}", prentOrderId);
                return HttpResult.error("订单商品明细不存在");
            }
            log.info("获取订单商品明细信息成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(items);
        } catch (Exception e) {
            log.error("获取订单商品明细信息失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("获取订单商品明细信息失败: " + e.getMessage());
        }
    }


    @GetMapping("/order/item/{itemId}")
    @Operation(summary = "根据itemId获取订单item详情", description = "供其他服务调用，根据itemId获取订单item详情")
    public HttpResult<SysOrderItem> getOrderItemDetail(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("itemId") String itemId) {
        log.info("获取订单商品明细信息: itemId={}", itemId);
        try {
            SysOrderItem item = apiOrdersService.getOrderItemDetails(itemId);
            log.info("获取订单商品明细信息成功: itemId={}", itemId);
            return HttpResult.ok(item);
        } catch (Exception e) {
            log.error("获取订单商品明细信息失败: itemId={}, error={}", itemId, e.getMessage(), e);
            return HttpResult.error("获取订单商品明细信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取 订单信息
     *
     * @param prentOrderId 订单ID
     * @return 订单信息
     */
    @GetMapping("/order/group/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单信息", description = "供其他服务调用，获取订单信息")
    public HttpResult<SubOrderGroupVo> getOrderGroupByOrderId(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("获取订单信息: prentOrderId={}", prentOrderId);
        try {
            SubOrderGroupVo dto = apiOrdersService.getOrderGroupByPrentOrderId(prentOrderId);
            if (BeanUtil.isEmpty(dto)) {
                log.warn("订单订单信息 不存在: prentOrderId={}", prentOrderId);
                return HttpResult.error("订单信息 不存在");
            }
            log.info("获取订单信息 成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(dto);
        } catch (Exception e) {
            log.error("获取订单信息 失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("获取订单信息 失败: " + e.getMessage());
        }
    }

    /**
     * 自定义获取订单信息列表
     * 根据状态、等条件查询b2b订单列表，支持分页
     *
     * @param request 查询条件
     * @return 订单信息列表
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 自定义获取订单信息列表")
    @PostMapping("/order/list")
    @Operation(summary = "自定义获取订单信息列表", description = "供其他服务调用，根据状态、等条件查询b2b订单列表，支持分页")
    public HttpResult<IPage<KnetB2bOrderQueryVo>> queryB2bOrderList(@Validated @RequestBody KnetB2bOrderQueryRequest request) {
        log.info("自定义获取订单信息列表: {}", request);
        try {
            // 如果订单状态为已取消，直接返回空分页结果
            if (KnetOrderItemStatus.CANCELLED.equals(request.getStatus())) {
                log.info("订单状态为已取消，返回空列表");
                Page<KnetB2bOrderQueryVo> emptyPage = new Page<>(request.getPageNo(), request.getPageSize());
                return HttpResult.ok(emptyPage);
            }
            IPage<KnetB2bOrderQueryVo> page = apiOrdersService.getOrderItemsByPage(request);
            log.info("自定义获取订单信息列表 成功:返回条数 {}", page.getSize());
            return HttpResult.ok(page);
        } catch (Exception e) {
            log.error("<自定义获取订单信息列表> 失败: {}", e.getMessage(), e);
            return HttpResult.error("<自定义获取订单信息列表> 失败" + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     *
     * @param request 更新订单状态请求
     * @return 更新结果
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 更新订单状态")
    @PostMapping("/order/status")
    @Operation(summary = "更新订单状态", description = "供其他服务调用，更新订单状态")
    public HttpResult<Boolean> updateOrderStatus(@Validated @RequestBody UpdatedOrderRequest request) {
        log.info("更新订单状态: {}", request);
        try {
            log.info("更新订单状态 成功");
            boolean updated = apiOrdersService.updateOrderStatus(request);
            if (!updated) {
                log.info("更新订单状态 失败");
                return HttpResult.error("更新订单状态 失败");
            }
            return HttpResult.ok(true);
        } catch (Exception e) {
            log.error("更新订单状态 失败: {}", e.getMessage(), e);
            return HttpResult.error("更新订单状态 失败" + e.getMessage());
        }
    }

    /**
     * 根据父订单号获取订单item详情列表
     *
     * @param prentOrderId 父订单号
     * @return 订单项详情
     */
    @GetMapping("/order/items/{prentOrderId}")
    @Operation(summary = "根据父订单号获取订单item详情列表", description = "供其他服务调用，根据父订单号获取订单item详情列表")
    HttpResult<List<SysOrderItem>> getOrderItemList(
            @Parameter(description = "父订单号", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId) {
        log.info("根据父订单号获取订单item详情列表: prentOrderId={}", prentOrderId);
        try {
            List<SysOrderItem> items = apiOrdersService.queryOrderItemsByPrentOrderId(prentOrderId);
            log.info("根据父订单号获取订单item详情列表 成功: prentOrderId={}", prentOrderId);
            return HttpResult.ok(items);
        } catch (Exception e) {
            log.error("根据父订单号获取订单item详情列表 失败: prentOrderId={}, error={}", prentOrderId, e.getMessage(), e);
            return HttpResult.error("根据父订单号获取订单item详情列表 失败" + e.getMessage());
        }
    }

    /**
     * 订单超卖替换
     *
     * @param request 替换请求
     * @return 替换结果
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "订单超卖替换")
    @PostMapping("/order/replace-product")
    @Operation(summary = "订单超卖替换", description = "第三方传参数订单itemNo和新oneId替换订单对应的oneId与listingId")
    public HttpResult<ReplaceProductResp> replaceOrderProduct(
            @Validated @RequestBody ReplaceProductRequest request) {
        log.info("订单超卖替换: {}", request);
        try {
            ReplaceProductResp result = apiOrdersService.replaceOrderProduct(request);
            log.info("订单超卖替换 替换订单商品成功: itemNo={}, newOneId={}", request.getItemNo(), request.getNewOneId());
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("订单超卖替换 替换订单商品失败: {}, error={}", request, e.getMessage(), e);
            return HttpResult.error("订单超卖替换 替换订单商品失败: " + e.getMessage());
        }
    }
}
