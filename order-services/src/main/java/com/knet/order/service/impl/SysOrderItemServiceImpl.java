package com.knet.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.OrderItemDataVo;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.system.handler.ReversePriceCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderItemServiceImpl extends ServiceImpl<SysOrderItemMapper, SysOrderItem> implements ISysOrderItemService {

    @Resource
    private RandomStrUtil randomStrUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemDataVo> orderItemDataList, String parentOrderId) {
        List<SysOrderItem> orderItems = new ArrayList<>();
        for (OrderItemDataVo itemData : orderItemDataList) {
            // 根据数量创建多条记录，每条记录的count都是1
            for (int i = 0; i < itemData.getQuantity(); i++) {
                String itemOrderNo = randomStrUtil.getItemOrderId();
                // 使用原始价格计算KG和卖家到手价
                BigDecimal kgOwingPrice = ReversePriceCalculator.getKgOwningPrice(itemData.getUnitPrice());
                BigDecimal sellerOwingPrice = ReversePriceCalculator.getSellerOwingPrice(itemData.getOriginalPrice());
                SysOrderItem orderItem = SysOrderItem.createSysOrderItem(subOrderId, parentOrderId, itemData, itemOrderNo, kgOwingPrice, sellerOwingPrice);
                orderItems.add(orderItem);
            }
        }
        this.saveBatch(orderItems);
        return orderItems;
    }

    @Override
    public List<SysOrderItem> getOrderItemsByPrentOrderId(String prentOrderId) {
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getParentOrderId, prentOrderId)
                .notExists("SELECT 1 " +
                        "FROM sys_shipping_item_rel rel " +
                        "WHERE rel.item_id = sys_order_item.item_id " +
                        "AND rel.label_id IS NULL " +
                        "AND rel.del_flag = 0");
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOrderStatusByParentId(String parentOrderId, KnetOrderItemStatus status) {
        log.info("更新item订单 状态: parentOrderId={}, status={}", parentOrderId, status.getName());
        try {
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderItem::getParentOrderId, parentOrderId)
                    .set(SysOrderItem::getStatus, status)
                    .set(KnetOrderItemStatus.PAID.equals(status), SysOrderItem::getPaidTime, new Date())
                    .set(KnetOrderItemStatus.CANCELLED.equals(status), SysOrderItem::getCancelledTime, new Date());
            boolean updated = this.update(updateWrapper);
            log.info("更新item订单 状态更新结果: parentOrderId={}, status={}, result={}", parentOrderId, status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新item订单 状态失败: parentOrderId={}, status={}, error={}", parentOrderId, status.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据订单项ID列表更新订单明细状态
     *
     * @param itemIds 订单项ID列表
     * @param status  新状态
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOrderStatusByItemIds(List<Long> itemIds, KnetOrderItemStatus status) {
        if (itemIds == null || itemIds.isEmpty()) {
            log.warn("更新订单项状态失败: 订单项ID列表为空");
            return false;
        }

        log.info("根据订单项ID列表更新订单状态: itemIds数量={}, status={}", itemIds.size(), status.getName());
        try {
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .in(SysOrderItem::getItemId, itemIds)
                    .set(SysOrderItem::getStatus, status)
                    .set(KnetOrderItemStatus.CANCELLED.equals(status), SysOrderItem::getCancelledTime, new Date());
            boolean updated = this.update(updateWrapper);
            log.info("根据订单项ID列表更新订单状态结果: itemIds数量={}, status={}, result={}",
                    itemIds.size(), status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("根据订单项ID列表更新订单状态失败: itemIds数量={}, status={}, error={}",
                    itemIds.size(), status.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据订单项编号获取订单项信息
     */
    @Override
    public SysOrderItem getOrderItemByItemNo(String itemNo) {
        return baseMapper.selectByItemNo(itemNo);
    }

    /**
     * 更新订单项的商品信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderItem(String itemNo, String newOneId, String newListingId) {
        int updatedRows = baseMapper.updateProductInfo(itemNo, newOneId, newListingId);
        if (updatedRows == 0) {
            throw new ServiceException("更新订单项失败，可能订单项不存在或已被修改");
        }
    }
}
