package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.enums.KnetCurrencyCode;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.service.PricingStrategyService;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.OffSaleKnetProductResp;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import com.knet.goods.service.IKnetProductOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_CACHE_KEY_PREFIX;
import static com.knet.goods.model.entity.KnetProduct.initKnetProduct;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:18
 * @description: Product operation service 实现类 - 处理所有非查询操作
 */
@Slf4j
@Service
public class KnetProductOperationServiceImpl extends ServiceImpl<KnetProductMapper, KnetProduct> implements IKnetProductOperationService {
    @Resource
    private KnetProductMapper baseMapper;
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private PricingStrategyService pricingStrategyService;

    @Override
    public KnetProduct createByKnet(CreateKnetProductRequest.ProductDto request) {
        if (!KnetCurrencyCode.USD.equals(request.getCurrency())) {
            throw new ServiceException("暂时只支持美元");
        }
        KnetProduct knetProduct = initKnetProduct(request);
        knetProduct.setListingId(randomStrUtil.getProductId());
        if (KnetCurrencyCode.USD.equals(request.getCurrency())) {
            knetProduct.setPrice(request.getPrice());
        }
        log.info("🆕 商品创建 | listingId: {} | sku: {} | spec: {} | 价格: {} | 状态: {}",
                knetProduct.getListingId(), knetProduct.getSku(),
                knetProduct.getSpec(), knetProduct.getPrice(), knetProduct.getStatus());
        return knetProduct;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertIgnoreBatch(List<KnetProduct> list) {
        if (CollUtil.isEmpty(list)) {
            log.warn("⚠️ 批量插入商品 | 商品列表为空 | ");
            return;
        }
        log.info("🔄 批量插入商品 || 商品数量: {}", list.size());
        try {
            baseMapper.insertIgnoreBatch(list);
            log.info("✅ 批量插入商品完成 | 成功插入商品数: {} | 操作时间: {}", list.size(), new Date());
        } catch (Exception e) {
            log.error("❌ 批量插入商品失败 | 商品数量: {} | 错误信息: {}", list.size(), e.getMessage(), e);
            throw new ServiceException("批量插入商品失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateKnetProductForOffSale(List<OffSaleKnetProductRequest.ProductDto> products) {
        List<String> listingIds = products.stream()
                .map(OffSaleKnetProductRequest.ProductDto::getListingId)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        log.info("🔄 商品下架操作 | | 请求商品数: {} | listingIds: {}",
                listingIds.size(), listingIds);
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getListingId, listingIds)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
        try {
            int updatedCount = baseMapper.update(null, updateWrapper);
            log.info("✅ 商品下架操作完成 | 实际更新商品数: {} ", updatedCount);
        } catch (Exception e) {
            log.error("❌ 商品下架操作失败 | listingIds: {} | 错误信息: {}", listingIds, e.getMessage(), e);
            throw new ServiceException("商品下架失败");
        }
        return listingIds;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public OffSaleKnetProductResp.ProductDto processKnetProductPrice(UpdatePriceKnetProductRequest.ProductDto productDto) {
        if (!KnetCurrencyCode.USD.equals(productDto.getCurrency())) {
            throw new ServiceException("价格单位只能是美元");
        }
        // 获取变更前的商品信息
        KnetProduct beforeProduct = this.getOne(
                new LambdaQueryWrapper<KnetProduct>()
                        .eq(KnetProduct::getListingId, productDto.getListingId())
                        .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
        );
        if (beforeProduct == null) {
            log.warn("❌ 商品价格更新失败 | 商品不存在或已下架 | listingId: {} | ",
                    productDto.getListingId());
            throw new ServiceException("更新商品价格失败，商品不存在或已下架,失败的listingId:" + productDto.getListingId());
        }
        log.info("💰 商品价格更新 |  | listingId: {} | sku: {} | spec: {} | 价格变更: {} -> {}",
                productDto.getListingId(), beforeProduct.getSku(),
                beforeProduct.getSpec(), beforeProduct.getPrice(), productDto.getPrice());

        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getListingId, productDto.getListingId())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getPrice, productDto.getPrice());
        boolean updated = this.update(null, updateWrapper);
        if (!updated) {
            log.error("❌ 商品价格更新失败 | 数据已被其他操作修改 | listingId: {} |  | 尝试更新价格: {}",
                    productDto.getListingId(), productDto.getPrice());
            throw new ServiceException("更新商品价格失败，数据已经被其他操作修改,失败的listingId:" + productDto.getListingId());
        }
        log.info("✅ 商品价格更新成功 | listingId: {} | 价格变更: {} -> {} ",
                productDto.getListingId(), beforeProduct.getPrice(), productDto.getPrice());
        return new OffSaleKnetProductResp.ProductDto(productDto.getListingId(), true, productDto.getPrice());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateKnetProductForSysSkuInfo(List<SysUpdateSysSkuEvents> updateEvents) {
        if (CollUtil.isEmpty(updateEvents)) {
            return;
        }
        updateEvents.forEach(event -> {
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getSku, event.getSku())
                    .set(StrUtil.isNotBlank(event.getBrand()), KnetProduct::getBrand, event.getBrand())
                    .set(StrUtil.isNotBlank(event.getRemarks()), KnetProduct::getRemarks, event.getRemarks());
            this.update(null, updateWrapper);
            String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, event.getSku());
            if (RedisCacheUtil.hasKey(redisSysSkuKey)) {
                RedisCacheUtil.del(redisSysSkuKey);
            }
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void setProductModifyMark(List<String> skus, ProductMark productMark) {
        if (CollUtil.isEmpty(skus)) {
            return;
        }
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getSku, skus)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(null != productMark, KnetProduct::getMark, productMark);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void resetProductMarkToCommon(ProductMark productMark) {
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getMark, productMark)
                .set(null != productMark, KnetProduct::getMark, ProductMark.COMMON);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public int updateExistingProductsToOffSale(List<String> oneIds) {
        if (CollUtil.isEmpty(oneIds)) {
            return 0;
        }
        try {
            // 查询已存在且状态为ON_SALE的oneId列表
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
            List<KnetProduct> existingProducts = baseMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(existingProducts)) {
                log.info("没有找到需要下架的商品");
                return 0;
            }
            log.info("找到 {} 个需要下架的商品", existingProducts.size());
            // 将这些商品的状态设置为OFF_SALE
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
            int updatedCount = baseMapper.update(null, updateWrapper);
            log.info("成功下架 {} 个商品", updatedCount);
            return updatedCount;
        } catch (Exception e) {
            log.error("下架已存在商品失败: {}", e.getMessage(), e);
            throw new ServiceException("下架已存在商品失败");
        }
    }

    @DistributedLock(key = "'inventory:legacy:lock:' + #request.hashCode()", expire = 30)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public boolean lockInventory(CheckAndLockInvRequest request) {
        if (CollUtil.isEmpty(request.getItems())) {
            log.warn("⚠️ 库存锁定 | 商品列表为空 ");
            return false;
        }
        log.info("🔒 库存锁定开始 | 商品种类数: {} | 总订单数量: {}",
                request.getItems().size(),
                request.getItems().stream().mapToInt(SubOrderItemResp::getCount).sum());
        request.getItems().forEach(item -> {
            // 将策略价格转换为原始价格进行库存匹配
            Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
            Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
            log.info("库存锁定价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                    item.getSku(), strategyPriceCents, originalPriceCents);
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(KnetProduct::getSku, item.getSku())
                    .eq(KnetProduct::getPrice, originalPriceCents)
                    .eq(KnetProduct::getSpec, item.getSize())
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .orderByDesc(KnetProduct::getCreateTime)
                    .last("LIMIT " + item.getCount());
            List<KnetProduct> productsToLock = baseMapper.selectList(queryWrapper);
            if (productsToLock.size() < item.getCount()) {
                log.error("❌ 库存锁定失败 | SKU: {} | 尺码: {} | 原始价格: {} | 请求数量: {} | 可用数量: {} | 原因: 库存不足",
                        item.getSku(), item.getSize(), originalPriceCents, item.getCount(), productsToLock.size());
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
            }
            List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
            try {
                // 执行锁定操作，确保只锁定ON_SALE状态的商品
                LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                // 再次确认状态为ON_SALE
                updateWrapper
                        .in(KnetProduct::getId, idsToLock)
                        .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                        .set(KnetProduct::getStatus, ProductStatus.LOCKED);
                int updatedCount = baseMapper.update(null, updateWrapper);
                if (updatedCount != idsToLock.size()) {
                    log.error("商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定",
                            idsToLock.size(), updatedCount);
                    throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
                }
                log.info("✅ 库存锁定成功 | SKU: {} | 尺码: {} | 原始价格: {} | 锁定数量: {} | 商品ID: {}",
                        item.getSku(), item.getSize(), originalPriceCents, updatedCount, idsToLock);
            } catch (Exception e) {
                log.error("❌ 商品锁定失败 | SKU: {} | 尺码: {} | 原始价格: {} | 错误信息: {}",
                        item.getSku(), item.getSize(), originalPriceCents, e.getMessage(), e);
                throw new ServiceException("锁定库存失败");
            }
        });
        log.info("🔒 库存锁定完成 | 所有商品锁定成功 |");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProductBasedOnExisting(CreateProductBasedOnExistingRequest request) {
        KnetProduct sourceProduct = getProductByOneId(request.getSourceOneId());
        if (sourceProduct == null) {
            throw new ServiceException("源商品不存在: " + request.getSourceOneId());
        }
        //  复制商品信息，修改oneId和listingId，设置为超卖替换状态
        KnetProduct newProduct = BeanUtil.copyProperties(sourceProduct, KnetProduct.class);
        newProduct.setId(null);
        newProduct.setOneId(request.getTargetOneId());
        newProduct.setListingId(randomStrUtil.getProductId());
        newProduct.setStatus(ProductStatus.LOCKED);
        newProduct.setMark(ProductMark.OVERSELL_REPLACEMENT);
        newProduct.setCreateTime(new Date());
        newProduct.setUpdateTime(new Date());
        baseMapper.insert(newProduct);
        log.info("基于现有商品创建新商品成功(超卖替换): sourceOneId={}, targetOneId={}, newListingId={}, status={}, mark={}",
                request.getSourceOneId(), request.getTargetOneId(), newProduct.getListingId(),
                newProduct.getStatus(), newProduct.getMark());
        return newProduct.getListingId();
    }

    @Override
    public KnetProduct getProductByOneId(String oneId) {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(KnetProduct::getOneId, oneId)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 将已上架商品锁定并标记为超卖替换
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockProductForOversellReplacement(String oneId) {
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getOneId, oneId)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getStatus, ProductStatus.LOCKED)
                .set(KnetProduct::getMark, ProductMark.OVERSELL_REPLACEMENT);
        int updatedCount = baseMapper.update(null, updateWrapper);
        if (updatedCount > 0) {
            log.info("商品锁定成功(超卖替换): oneId={}, 更新数量={}", oneId, updatedCount);
        } else {
            log.warn("商品锁定失败，可能商品不存在或状态已变更: oneId={}", oneId);
        }
    }
}
